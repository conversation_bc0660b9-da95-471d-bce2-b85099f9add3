import { describe, expect, it } from "vitest";

import { sampleWorkspaceDocument } from "@src/hooks/formConfiguration/select/useConfigurationOptions.test";

import { WorkspaceConfigurationHelper } from "./WorkspaceConfigurationHelper";

describe("WorkspaceConfigurationHelper", () => {
  const helper = new WorkspaceConfigurationHelper(sampleWorkspaceDocument);

  it("should get a form by id", () => {
    const form = helper.getForm("form-1");
    expect(form).toBeDefined();
    expect(form?.id).toBe("form-1");
  });

  it("should throw an error if form id is not found", () => {
    expect(helper.getForm("form-3")).toBeUndefined();
  });

  it("should list foundations as options", () => {
    const options = helper.listFoundationsAsOptions();
    expect(options).toHaveLength(2);
    expect(options[0].value).toBe("foundation-1");
    expect(options[0].label).toBe("Client");
    expect(options[1].value).toBe("foundation-2");
    expect(options[1].label).toBe("Partner");
  });

  it("should list forms as options", () => {
    const options = helper.listFormsAsOptions();
    expect(options).toHaveLength(2);
    expect(options[0].value).toBe("form-1");
    expect(options[0].label).toBe("General");
    expect(options[1].value).toBe("form-2");
    expect(options[1].label).toBe("Specific");
  });

  it("should list forms as options filtered by foundation id", () => {
    const options = helper.listFormsAsOptions("foundation-1");
    expect(options).toHaveLength(2);
  });

  it("should list form questions as options", () => {
    const options = helper.listFormQuestionsAsOptions("form-1");
    expect(options).toHaveLength(2);
    expect(options).toContainEqual({
      value: "F1-Q1",
      label: "f1 q1 text",
      description: "Untitled - ui.configuration.forms.question.type.text"
    });
    expect(options).toContainEqual({
      value: "F1-Q2",
      label: "f1 q2 text",
      description: "Untitled - ui.configuration.forms.question.type.text"
    });
  });

  it("should return empty array for non-existing form id", () => {
    const options = helper.listFormQuestionsAsOptions("form-3");
    expect(options).toHaveLength(0);
  });

  it("should return empty array for undefined form id", () => {
    const options = helper.listFormQuestionsAsOptions(undefined);
    expect(options).toHaveLength(0);
  });

  it("should get foundation configuration by id", () => {
    const foundation = helper.getFoundationConfiguration("foundation-1");
    expect(foundation).toBeDefined();
    expect(foundation.id).toBe("foundation-1");
  });

  it("should get the next foundation configuration", () => {
    const nextFoundation =
      helper.getNextFoundationConfiguration("foundation-1");
    expect(nextFoundation).toBeDefined();
    expect(nextFoundation?.id).toBe("foundation-2");
  });

  it("should return undefined for next foundation if it is the last one", () => {
    const nextFoundation =
      helper.getNextFoundationConfiguration("foundation-2");
    expect(nextFoundation).toBeUndefined();
  });

  it("should get series config by id", () => {
    const seriesConfig = helper.findSeriesConfig("series-1");
    expect(seriesConfig).toBeDefined();
    expect(seriesConfig?.id).toBe("series-1");
  });

  it("should list series as options", () => {
    const options = helper.listSeriesAsOptions();
    expect(options).toHaveLength(2);
    expect(options[0].value).toBe("series-1");
    expect(options[0].label).toBe("Series 1");
    expect(options[1].value).toBe("series-2");
    expect(options[1].label).toBe("Series 2");
  });

  it("should list intervals as options from formConfigurationId", () => {
    const options = helper.listFormIntervalsAsOptions("form-1");
    expect(options).toHaveLength(2);
    expect(options[0].value).toBe("s1-interval-1");
    expect(options[0].label).toBe("Interval 1");
    expect(options[1].value).toBe("s1-interval-2");
    expect(options[1].label).toBe("Interval 2");
  });

  it("should list intervals as options from seriesConfigurationId", () => {
    const options = helper.listSeriesIntervalsAsOptions("series-2");
    expect(options).toHaveLength(2);
    expect(options[0].value).toBe("s2-interval-1");
    expect(options[0].label).toBe("Series2 Interval 1");
    expect(options[1].value).toBe("s2-interval-2");
    expect(options[1].label).toBe("Series2 Interval 2");
  });

  it("should get interval by id", () => {
    const interval = helper.getIntervalById("s1-interval-1");
    expect(interval).toBeDefined();
    expect(interval?.id).toBe("s1-interval-1");
  });

  it("should return undefined for non-existing interval id", () => {
    const interval = helper.getIntervalById("s3-interval-1");
    expect(interval).toBeUndefined();
  });

  it("should get series by interval id", () => {
    const series = helper.getSeriesByIntervalId("s1-interval-1");
    expect(series).toBeDefined();
    expect(series?.id).toBe("series-1");
  });

  it("should return undefined for non-existing interval id in series", () => {
    const series = helper.getSeriesByIntervalId("s3-interval-1");
    expect(series).toBeUndefined();
  });

  it("should check if it is the last foundation level", () => {
    const isLast = helper.isLastFoundationLevel("foundation-1");
    expect(isLast).toBe(false);
  });

  it("should return true if it is the last foundation level", () => {
    const isLast = helper.isLastFoundationLevel("foundation-2");
    expect(isLast).toBe(true);
  });
});
