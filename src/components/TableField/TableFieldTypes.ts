import { SelectOptions } from "../Select";

export enum TableFieldVariant {
  DEFAULT = "default",
  LIST = "list"
}

export type TableFieldColumnId = string;

export enum TableFieldCellType {
  TEXT = "text",
  NUMBER = "number",
  BOOLEAN = "boolean",
  SELECT = "select",
  MULTI_SELECT = "multiSelect",
  DATE = "date",
  DATE_RANGE = "dateRange",
  CUSTOM = "custom"
}

export type TableFieldCellValue = string | boolean | string[] | unknown;

export type TableFieldColumn = {
  label: string;
  id: TableFieldColumnId;
  name: string;
  description?: string;
  tooltip?: string;
  type: `${TableFieldCellType}`;
  properties?: {
    required?: boolean;
    disabled?: boolean;
    hidden?: boolean;
    options?: SelectOptions;
  };
  highlightComponents?: React.ReactNode;
  customGetValue?: (value: TableFieldCellValue) => TableFieldCellValue;
  customCellRenderer?: (props: {
    // value: TableFieldCellValue | undefined;
    // onChange: ((value: TableFieldCellValue) => void) | undefined;
    // autoFocus: boolean | undefined;
    // onFocus: React.FocusEventHandler<Element> | undefined;
    // onBlur: React.FocusEventHandler<Element> | undefined;
    // onPaste: React.ClipboardEventHandler<Element> | undefined;
    // onKeyDown: React.KeyboardEventHandler<Element> | undefined;
    // onlyTriggerChangeWhenBlur: boolean | undefined;
    // disabled: boolean | undefined;
    // controlFocus: boolean | undefined;
    value: any;
    onChange?: (value: any) => void;
    disabled?: boolean;
    rowIndex: number;
    columnId: string;
    answerAccessor: string;
    question: any;
    location: {
      variant: string;
      questionId: string;
      rowId: string;
      columnId: string;
    };
  }) => React.ReactNode;
};

export type TableFieldColumns = Array<TableFieldColumn>;

export type TableFieldRowValue = {
  [key: TableFieldColumnId]: TableFieldCellValue;
};

export type TableFieldValue = Array<TableFieldRowValue>;

export type TableFieldCellLocation = {
  rowIndex: number;
  columnIndex: number;
};

export type TableFieldHighlightedCells = {
  from: TableFieldCellLocation;
  to: TableFieldCellLocation;
};

export type TableFieldSetTable = (
  value: TableFieldValue,
  options: {
    fromRowIndex?: number;
  }
) => void;

export type TableFieldSetRow = (
  rowValue?: TableFieldRowValue,
  rowIndex?: number,
  options?: {
    newRow?: boolean;
    // From specific column - for pasting (keep values in the other columns)
    keepOtherRowValues?: boolean;
  }
) => void;

export type TableFieldRemoveRow = (rowIndex: number) => void;

export type TableFieldReorderRow = (
  rowIndexToMove: number,
  toRowIndex: number
) => void;

export type TableFieldSetCell = (
  value: TableFieldCellValue | undefined,
  rowIndex: number,
  columnId: TableFieldColumnId
) => void;

export type TableFieldSetColumn = (
  columnValue: TableFieldCellValue[],
  columnId: TableFieldColumnId,
  options?: {
    keepOtherColumnValues?: boolean;
    // From specific row - for pasting
    fromRowIndex?: number;
  }
) => void;

export type TableFieldColumnWidths =
  | {
      [columnId: TableFieldColumnId]: number;
    }
  | undefined;

export type TableFieldOnChangeColumnWidth = (
  columnId: string,
  width?: number
) => void;

export type TableFieldTranslations = {
  addRowAbove?: string;
  addRowBelow?: string;
  moveUp?: string;
  moveDown?: string;
  clearCells?: string;
  deleteRow?: string;
  booleanField?: {
    trueLabel?: string;
    falseLabel?: string;
  };
};
