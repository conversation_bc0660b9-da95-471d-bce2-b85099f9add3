import React, { useEffect, useState } from "react";

import {
  DatePicker,
  DateRangePicker,
  DateRangePickerValue
} from "../DatePicker";
import { NumberField } from "../NumberField";
import { MultiSelect, MultiSelectValue, Select } from "../Select";
import { TextAreaField } from "../TextAreaField";
import {
  TableFieldCellType,
  TableFieldCellValue,
  TableFieldColumn,
  TableFieldTranslations
} from "./TableFieldTypes";

export const TableFieldCell = ({
  column,
  value,
  onChange,
  focused,
  onFocus,
  onBlur,
  onPaste,
  onlyTriggerChangeWhenBlur,
  onKeyDown,
  disabled,
  translations
}: {
  column: TableFieldColumn;
  value?: TableFieldCellValue;
  onChange?: (value: TableFieldCellValue) => void;
  focused?: boolean;
  onFocus?: React.FocusEventHandler;
  onBlur?: React.FocusEventHandler;
  onPaste?: React.ClipboardEventHandler;
  onlyTriggerChangeWhenBlur?: boolean;
  onKeyDown?: React.KeyboardEventHandler;
  disabled?: boolean;
  translations: TableFieldTranslations;
}) => {
  const [controlFocus, setControlFocus] = useState<boolean | undefined>(
    undefined
  );

  useEffect(() => {
    if (focused === true) {
      setControlFocus(true);
      setTimeout(() => {
        setControlFocus(undefined);
      });
    }
  }, [focused]);

  if (column.customCellRenderer) {
    console.log("value", value);
    return column.customCellRenderer({
      value,
      onChange,
      autoFocus: focused,
      onFocus,
      onPaste,
      onBlur,
      onKeyDown,
      onlyTriggerChangeWhenBlur,
      disabled: disabled || column.properties?.disabled,
      controlFocus
    });
  }
  if (column.type === TableFieldCellType.TEXT) {
    return (
      <TextAreaField
        width="100"
        value={value as string}
        onChange={onChange}
        autoFocus={focused}
        onFocus={onFocus}
        onPaste={onPaste}
        onBlur={onBlur}
        onlyTriggerChangeWhenBlur={onlyTriggerChangeWhenBlur}
        onKeyDown={onKeyDown}
        controlFocus={controlFocus}
        disabled={disabled || column.properties?.disabled}
      />
    );
  } else if (column.type === TableFieldCellType.NUMBER) {
    return (
      <NumberField
        width="100"
        value={value as string}
        onChange={onChange}
        autoFocus={focused}
        onFocus={onFocus}
        onPaste={onPaste}
        onBlur={onBlur}
        onlyTriggerChangeWhenBlur={onlyTriggerChangeWhenBlur}
        onKeyDown={onKeyDown}
        controlFocus={controlFocus}
        disabled={disabled || column.properties?.disabled}
      />
    );
  } else if (column.type === TableFieldCellType.BOOLEAN) {
    return (
      <Select
        width="100"
        value={String(value ?? "")}
        options={[
          {
            value: "true",
            label: translations.booleanField?.trueLabel ?? "TRUE"
          },
          {
            value: "false",
            label: translations.booleanField?.falseLabel ?? "FALSE"
          }
        ]}
        onChange={value => {
          onChange?.(value as TableFieldCellValue);
        }}
        autoFocus={focused}
        onFocus={onFocus}
        onPaste={onPaste}
        onBlur={onBlur}
        onlyTriggerChangeWhenBlur={onlyTriggerChangeWhenBlur}
        onKeyDown={onKeyDown}
        controlFocus={controlFocus}
        autoOpenOnFocus={false}
        disabled={disabled || column.properties?.disabled}
      />
    );
  } else if (column.type === TableFieldCellType.SELECT) {
    return (
      <Select
        width="100"
        value={String(value ?? "")}
        onChange={value => {
          onChange?.(value as TableFieldCellValue);
        }}
        onFocus={onFocus}
        onBlur={onBlur}
        onKeyDown={onKeyDown}
        autoFocus={focused}
        onPaste={onPaste}
        onlyTriggerChangeWhenBlur={onlyTriggerChangeWhenBlur}
        controlFocus={controlFocus}
        autoOpenOnFocus={false}
        disabled={disabled || column.properties?.disabled}
        options={column.properties?.options}
      />
    );
  } else if (column.type === TableFieldCellType.MULTI_SELECT) {
    return (
      <MultiSelect
        width="100"
        value={value as MultiSelectValue | undefined}
        options={column.properties?.options}
        onChange={value => onChange?.(value as TableFieldCellValue)}
        onFocus={onFocus}
        onBlur={onBlur}
        onKeyDown={onKeyDown}
        autoFocus={focused}
        onPaste={onPaste}
        onlyTriggerChangeWhenBlur={onlyTriggerChangeWhenBlur}
        controlFocus={controlFocus}
        autoOpenOnFocus={false}
        disabled={disabled || column.properties?.disabled}
      />
    );
  } else if (column.type === TableFieldCellType.DATE) {
    return (
      <DatePicker
        width="100"
        value={value as string}
        onChange={onChange}
        autoFocus={focused}
        onFocus={onFocus}
        onPaste={onPaste}
        onBlur={onBlur}
        onlyTriggerChangeWhenBlur={onlyTriggerChangeWhenBlur}
        onKeyDown={onKeyDown}
        controlFocus={controlFocus}
        disabled={disabled || column.properties?.disabled}
      />
    );
  } else if (column.type === TableFieldCellType.DATE_RANGE) {
    return (
      <DateRangePicker
        width="100"
        value={value as DateRangePickerValue}
        onChange={onChange}
        autoFocus={focused}
        onFocus={onFocus}
        onPaste={onPaste}
        onBlur={onBlur}
        onlyTriggerChangeWhenBlur={onlyTriggerChangeWhenBlur}
        onKeyDown={onKeyDown}
        controlFocus={controlFocus}
        disabled={disabled || column.properties?.disabled}
      />
    );
  }
  return <>Type not yet supported</>;
};
