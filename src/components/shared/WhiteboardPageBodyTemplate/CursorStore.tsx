import { create } from "zustand";

export interface CursorState {
  cursorPosition: { x: number; y: number };
  updatePosition: (event: MouseEvent) => void;
}

export const useCursorStore = create<CursorState>(set => ({
  cursorPosition: { x: 0, y: 0 },
  updatePosition: (event: MouseEvent) =>
    set({ cursorPosition: { x: event.clientX, y: event.clientY } })
}));

document.addEventListener("mousemove", e => {
  useCursorStore.getState().updatePosition(e);
});
