import React, { ReactNode, useCallback, useEffect, useState } from "react";

import { CursorContext } from "./CursorContext";

export const CursorProvider = ({ children }: { children: ReactNode }) => {
  const [cursorPosition, setCursorPosition] = useState({ x: 0, y: 0 });

  const updatePosition = useCallback((event: MouseEvent) => {
    setCursorPosition({ x: event.clientX, y: event.clientY });
  }, []);

  useEffect(() => {
    document.addEventListener("mousemove", updatePosition);
    return () => document.removeEventListener("mousemove", updatePosition);
  }, [updatePosition]);
  const contextValue = React.useMemo(
    () => ({ cursorPosition }),
    [cursorPosition]
  );

  return (
    <CursorContext.Provider value={contextValue}>
      {children}
    </CursorContext.Provider>
  );
};
