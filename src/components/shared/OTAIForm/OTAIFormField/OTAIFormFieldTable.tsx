import React, { useEffect, useMemo } from "react";

import { Box, Label, Stack, TableField } from "@oneteam/onetheme";
import { useFormContext } from "react-hook-form";

import { QuestionTableAnswer } from "@pages/collection/forms/questions/CollectionQuestionBlock/QuestionTableAnswer";

import { useDictionary } from "@src/hooks/useDictionary";
import { Question } from "@src/types/Question";
import { TableQuestionProperties } from "@src/types/QuestionProperties";

import { OTAIFormFieldCommonProps } from "../OTAIFormType";
import "./OTAIFormFieldTable.scss";

const defaultArgs = {
  label: "Table Story",
  required: true,
  columns: [
    {
      label: "Name",
      name: "name",
      id: "name",
      type: "text",
      properties: {
        required: true
      }
    },
    {
      label: "Age",
      name: "age",
      id: "age",
      type: "number"
    },
    {
      label: "Moved house?",
      name: "movedHouse",
      id: "movedHouse",
      type: "boolean"
    },
    {
      label: "Address",
      name: "address",
      id: "address",
      type: "text"
    },
    {
      label: "Phone",
      name: "phone",
      id: "phone",
      type: "text"
    },
    {
      label: "Option",
      name: "option",
      id: "option",
      type: "select",
      properties: {
        options: [
          { label: "Option 1", value: "option1" },
          { label: "Option 2", value: "option2" }
        ]
      }
    },
    {
      label: "Multi-Option",
      name: "multiOption",
      id: "multiOption",
      type: "multiSelect",
      properties: {
        options: [
          { label: "Option 1", value: "option1" },
          { label: "Option 2", value: "option2" }
        ]
      }
    },
    {
      label: "Date",
      name: "date",
      id: "date",
      type: "date"
    },
    {
      label: "Date range",
      name: "dateRange",
      id: "dateRange",
      type: "dateRange"
    }
  ],
  value: [
    {
      name: "John Doe",
      age: "30",
      movedHouse: true
    },
    {
      name: "Jane Doe",
      age: "25",
      movedHouse: false
    }
  ]
};

export const OTAIFormFieldTable = ({
  id,
  question,
  commonProps
  // TODO: use onChange
  // onChange
}: {
  id: string;
  question: Question<TableQuestionProperties>;
  commonProps: OTAIFormFieldCommonProps;
  // onChange?: (value: unknown) => void;
}) => {
  const d = useDictionary();
  const { watch, setValue } = useFormContext();
  const answers = watch(id) ?? [{}];
  const showPreviewAnswerRow = useMemo(() => {
    const columns = question.properties?.columns ?? [];
    return columns.length > 0;
  }, [question.properties?.columns]);

  // useEffect(() => {
  //   setValue(id, undefined);
  // }, [id, question, setValue]);

  return (
    <Stack gap="025">
      <Label
        label={commonProps.label ?? ""}
        description={commonProps.description}
        required={commonProps.required}
        disabled={commonProps?.disabled}
      />
      <Box width="100" overflow="auto">
        <QuestionTableAnswer
          d={d}
          question={question}
          answer={answers}
          answerAccessor={id}
          showPreviewAnswerRow={showPreviewAnswerRow}
          // Currently only used for QuestionPreview
          disableAddRow={true}
          // onChange={onChange}
          disabled={commonProps.disabled}
        />
        {/* <TableField
          variant="list"
          id="tableListField"
          name="table-list-field"
          value={defaultArgs.value}
          setRow={(row, value) => {}}
          removeRow={row => {}}
          setCell={(row, column, value) => {}}
          columns={defaultArgs.columns}
        /> */}
      </Box>
    </Stack>
  );
};
