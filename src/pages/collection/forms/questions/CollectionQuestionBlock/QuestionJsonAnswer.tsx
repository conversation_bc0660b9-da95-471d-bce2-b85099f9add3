import React, { Suspense, useCallback, useEffect, useState } from "react";

import {
  Accordion,
  Alert,
  AlertBackground,
  AlertIcon,
  AlertVariant,
  Box,
  CustomAccordionTrigger,
  Inline,
  Loading,
  MultiSelectValue,
  OpenCloseIcon,
  Stack,
  Toggle,
  getClassNames
} from "@oneteam/onetheme";

import { customNanoId } from "@helpers/customNanoIdHelper";
import { updateAnswer } from "@helpers/forms/answerHelper";
import {
  generateSchema,
  getAnswersAsJson,
  isValidQuestionValue
} from "@helpers/jsonEditorHelper";

import { useCollectionFormContext } from "@pages/collection/forms/CollectionFormContext";
import { QuestionAnswer } from "@pages/collection/forms/questions/CollectionQuestionBlock/QuestionAnswer";

import { Dictionary } from "@src/hooks/useDictionary.tsx";
import { Question, QuestionTypes } from "@src/types/Question.ts";
import {
  DateQuestionProperties,
  DateQuestionRangeValue,
  JSONQuestionProperties,
  ListQuestionProperties
} from "@src/types/QuestionProperties.ts";
import {
  FilesAnswer,
  FormAnswer,
  JsonAnswer,
  ListAnswer,
  ListAnswerItem,
  QuestionAnswers,
  TableAnswer
} from "@src/types/collection/CollectionForm.ts";

import { HighlightAnnotationLocation } from "../../HighlightAnnotationLocation/HighlightAnnotationLocation";
import { LabelWithHighlight } from "../../LabelWithHighlight";
import { QuestionListAnswer } from "./QuestionListAnswer";

const JsonEditor = React.lazy(() =>
  import("@oneteam/onetheme").then(module => ({ default: module.JsonEditor }))
);

export const QuestionJsonAnswer = ({
  d,
  statusLine,
  question,
  answer,
  answerAccessor,
  isNested = false
}: {
  d: Dictionary;
  statusLine?: JSX.Element;
  question: Question<JSONQuestionProperties>;
  answer: FormAnswer<JsonAnswer>["value"];
  answerAccessor: string;
  isNested?: boolean;
}) => {
  const { docChange } = useCollectionFormContext();
  const [isExpanded, setIsExpanded] = useState<boolean>(true);
  useEffect(() => {
    docChange?.(d => {
      if (!d.answers[question.id] && question.properties && !isNested) {
        d.answers[question.id] = {
          questionId: question.id,
          value: newJsonAnswer(question.properties.items),
          type: question.type
        };
      }
    });
  }, [docChange, question, isNested]);

  const [jsonData, setJsonData] = useState<string>(
    JSON.stringify(getAnswersAsJson(question.properties, answer), null, 2) ?? ""
  );
  const [showJsonEditor, setShowJsonEditor] = useState<boolean>(false);
  const [errors, setErrors] = useState<string[]>([]);
  const defaultJsonError = "Invalid JSON.";
  const jsonSchema = generateSchema(question);
  const displayJsonQuestions = question.properties?.items?.filter(
    c => !c.properties?.hidden
  );
  const allowedKeys = displayJsonQuestions?.map(item => item.identifier);

  const { documentId } = useCollectionFormContext();

  const convertJsonToAnswers = useCallback(
    (
      parsedJson: {
        [questionId: string]: string | number | QuestionAnswers | undefined;
      },
      questionTypes: {
        [identifier: string]: {
          id: string;
          type: string;
        };
      },
      questionItems?: Question[]
    ) => {
      return Object.entries(parsedJson).reduce<
        Record<
          string,
          FormAnswer<
            | string
            | number
            | boolean
            | MultiSelectValue
            | DateQuestionRangeValue
            | FilesAnswer
            | TableAnswer
            | QuestionAnswers
            | ListAnswer
          >
        >
      >((acc, [identifier, questionValue]) => {
        const questionId = questionTypes[identifier].id;
        const questionType = questionTypes[identifier].type;
        let convertedValue;

        if (questionType === QuestionTypes.DATE && questionValue === "") {
          convertedValue = undefined;
        } else if (questionType === QuestionTypes.NUMBER) {
          convertedValue = String(questionValue);
        } else if (questionType === QuestionTypes.JSON) {
          convertedValue = convertJsonToAnswers(
            questionValue as {
              [questionId: string]:
                | string
                | number
                | QuestionAnswers
                | undefined;
            },
            questionTypes,
            (
              questionItems?.find(item => item.identifier === identifier)
                ?.properties as JSONQuestionProperties
            )?.items
          );
        } else if (questionType === QuestionTypes.LIST) {
          if (Array.isArray(questionValue)) {
            const entities: { [key: string]: ListAnswerItem } = {};
            const order: string[] = [];
            const findItemQuestion = (
              questionItems: Question[] | undefined,
              identifier: string
            ): Question => {
              return (
                questionItems?.find(item => item.identifier === identifier)
                  ?.properties as JSONQuestionProperties
              )?.items?.[0];
            };

            questionValue.forEach(item => {
              // Generate unique ID for each list item
              const itemId = customNanoId();
              const itemQuestion = findItemQuestion(questionItems, identifier);

              entities[itemId] = {
                id: itemId,
                item: {
                  [itemQuestion.id]: {
                    questionId: itemQuestion.id,
                    type: itemQuestion.type,
                    value: item
                  }
                }
              };

              order.push(itemId);
            });

            convertedValue = {
              entities,
              order
            } as ListAnswer;
          }
        }

        const formAnswer: FormAnswer<
          | string
          | number
          | boolean
          | MultiSelectValue
          | DateQuestionRangeValue
          | FilesAnswer
          | TableAnswer
          | QuestionAnswers
          | ListAnswer
        > = {
          questionId: questionId,
          value: convertedValue ?? questionValue,
          type: questionType as QuestionTypes
        };

        acc[questionId] = formAnswer;

        return acc;
      }, {});
    },
    []
  );

  const isValidJson = useCallback(
    (value: string) => {
      try {
        // check values are valid type
        for (const [key, parsedValue] of Object.entries(JSON.parse(value))) {
          if (!allowedKeys?.includes(key)) {
            continue;
          }
          const matchingItem = question.properties?.items.find(
            item => item.identifier == key
          );

          if (matchingItem) {
            const valid = isValidQuestionValue(
              matchingItem.type,
              parsedValue as string | number | boolean,
              matchingItem.properties?.required ?? false
            );
            if (!valid) {
              setErrors([defaultJsonError]);
              return valid; // stop once first invalid type is found
            } else {
              setErrors([]);
            }
          }
        }

        return true;
      } catch {
        return false;
      }
    },
    [question, defaultJsonError, allowedKeys]
  );

  // Before useEffect, add this:
  const setAnswersFromJson = useCallback(
    (jsonString: string | undefined) => {
      if (!jsonString) {
        return;
      }

      if (isValidJson(jsonString)) {
        const parsedJson = JSON.parse(jsonString);
        const entries = Object.fromEntries(
          Object.entries(parsedJson).filter(([key]) =>
            allowedKeys?.includes(key)
          )
        ) as { [key: string]: string | number };

        const getNestedQuestionTypes = (
          items: Question[]
        ): { [identifier: string]: { id: string; type: string } } => {
          return items.reduce<{
            [identifier: string]: { id: string; type: string };
          }>((acc, item) => {
            if (item.identifier) {
              acc[item.identifier] = {
                id: item.id,
                type: item.type
              };
            }

            // Handle nested JSON questions
            if (
              item.type === QuestionTypes.JSON &&
              (item.properties as JSONQuestionProperties)?.items
            ) {
              const nestedTypes = getNestedQuestionTypes(
                (item.properties as JSONQuestionProperties).items
              );
              return { ...acc, ...nestedTypes };
            }

            return acc;
          }, {});
        };

        const questionTypes = question?.properties?.items
          ? getNestedQuestionTypes(question.properties.items)
          : {};

        const updatedAnswer = convertJsonToAnswers(
          entries,
          questionTypes,
          question?.properties?.items
        );

        updateAnswer(question, updatedAnswer, answerAccessor, documentId);
        setJsonData(jsonString ?? {});

        question.properties?.items.forEach(item => {
          const id = item.id;
          const answerToUpdate = answer?.[id];
          const newValue = updatedAnswer[item.identifier]?.value;
          if (answerToUpdate && newValue) {
            answerToUpdate.value = newValue;
          }

          return item;
        });
      }
    },
    [
      question,
      isValidJson,
      convertJsonToAnswers,
      allowedKeys,
      answerAccessor,
      documentId,
      answer
    ]
  );

  const handleChange = useCallback(
    (value: string | null) => {
      const newErrors: string[] = [];
      if (!value) {
        setErrors(newErrors);
        return;
      }
      try {
        const parsedValue = JSON.parse(value);

        // Json schema validation does not support date validation, so we need to do it manually
        question.properties?.items.forEach(item => {
          if (
            item.type === "date" &&
            parsedValue[item.identifier] &&
            item.properties
          ) {
            const dateValue = new Date(parsedValue[item.identifier]);
            const dateProperties = item.properties as DateQuestionProperties;
            const minDate = dateProperties.min
              ? new Date(dateProperties.min)
              : null;
            const maxDate = dateProperties.max
              ? new Date(dateProperties.max)
              : null;

            if (
              (minDate && dateValue < minDate) ||
              (maxDate && dateValue > maxDate)
            ) {
              let range = "";
              if (minDate && maxDate) {
                range = `(${minDate.toISOString().slice(0, 10)} - ${maxDate.toISOString().slice(0, 10)})`;
              } else if (minDate) {
                range = `(> ${minDate.toISOString().slice(0, 10)})`;
              } else if (maxDate) {
                range = `(< ${maxDate.toISOString().slice(0, 10)})`;
              }
              newErrors.push(
                `Date for ${item.identifier} is not within the valid range ${range}.`
              );
            }
          }
        });
      } catch {
        newErrors.push(defaultJsonError);
      }
      setErrors(newErrors);
    },
    [question, defaultJsonError]
  );

  const trigger: CustomAccordionTrigger = useCallback(
    ({ isOpen, onClick }) => (
      <Inline
        width="100"
        onClick={onClick}
        style={{
          cursor: "pointer",
          paddingRight: "var(--spacing-200)",
          paddingBottom: isNested ? "6px" : "var(--spacing-150)",
          marginTop: isNested ? "12px" : "calc(var(--spacing-100) * -1)"
        }}
        alignment="left"
        gap="050"
        spaceBetween
      >
        <Inline width="100" gap="050" alignment="left" spaceBetween>
          <Inline alignment="left" gap="025">
            <OpenCloseIcon isOpen={isOpen} />
            <HighlightAnnotationLocation
              location={{
                variant: "question",
                questionId: question.id
                // TODO: handle row and column ids for table questions
              }}
            >
              <LabelWithHighlight
                className="collection-question-block__label"
                label={question?.text}
                required={question?.properties?.required}
                description={question?.description}
                tooltip={question?.tooltip}
              />
            </HighlightAnnotationLocation>
          </Inline>
          <Inline>
            {!isNested && (
              <Toggle
                label={d("ui.configuration.forms.question.json.toggle")}
                value={Boolean(showJsonEditor).toString() ?? "false"}
                isChecked={showJsonEditor}
                onChange={(jsonMode: boolean) => {
                  if (jsonMode) {
                    const updated = getAnswersAsJson(
                      question.properties,
                      answer
                    );
                    const data = JSON.stringify(updated ?? {}, null, 2);
                    setJsonData(data);
                    handleChange(data);
                    setShowJsonEditor(true);
                  } else {
                    setAnswersFromJson(jsonData);
                    setShowJsonEditor(false);
                  }
                }}
              />
            )}
          </Inline>
        </Inline>
      </Inline>
    ),
    [
      showJsonEditor,
      jsonData,
      answer,
      question,
      isNested,
      d,
      handleChange,
      setJsonData,
      setAnswersFromJson
    ]
  );

  return (
    <>
      {!isNested && statusLine}
      <td
        className="collection-question-block__td--json"
        colSpan={2}
        style={{ padding: "0" }}
      >
        <Stack
          style={
            isNested
              ? {
                  border: "1px solid var(--color-border)",
                  borderRight: "none",
                  paddingLeft: "var(--spacing-100)",
                  paddingBottom: "var(--spacing-100)"
                }
              : undefined
          }
        >
          <Accordion
            contentOverflow="visible"
            trigger={trigger}
            isOpen={isExpanded}
            onOpenChange={() => setIsExpanded(!isExpanded)}
          >
            {showJsonEditor ? (
              <Box
                position="relative"
                style={{ height: "15vh", width: "100%" }}
              >
                <Suspense fallback={<Loading size={24} />}>
                  <JsonEditor
                    localJSONContent={jsonData}
                    schema={jsonSchema}
                    onBlur={setAnswersFromJson}
                    onChange={(value: string | undefined) =>
                      handleChange(value ?? null)
                    }
                    height="90%"
                    disabled={question.properties?.disabled}
                  />
                </Suspense>
                {errors && (
                  <Alert
                    className="label__error"
                    variant={AlertVariant.DANGER}
                    background={AlertBackground.TRANSPARENT}
                    icon={AlertIcon.NONE}
                  >
                    {errors.map((error, index) => (
                      <p key={`json_${question.id}_error_${index}`}>{error}</p>
                    ))}
                  </Alert>
                )}
              </Box>
            ) : (
              answer &&
              typeof answer === "object" && (
                <Box>
                  {displayJsonQuestions?.map(jsonQuestion => {
                    return jsonQuestion.type === QuestionTypes.JSON ? (
                      <table
                        style={{ borderSpacing: "0" }}
                        key={jsonQuestion.id}
                      >
                        <tbody>
                          <tr>
                            <QuestionJsonAnswer
                              question={
                                jsonQuestion as Question<JSONQuestionProperties>
                              }
                              answer={
                                answer?.[jsonQuestion.id]?.value as JsonAnswer
                              }
                              answerAccessor={`${answerAccessor}.${jsonQuestion.id}.value`}
                              d={d}
                              isNested
                            />
                          </tr>
                        </tbody>
                      </table>
                    ) : (
                      <Inline
                        className={getClassNames([
                          "collection-question-block",
                          `collection-question-block--type-${jsonQuestion.type}-row`
                        ])}
                        key={jsonQuestion.id}
                        gap="200"
                      >
                        <Inline
                          className="collection-question-block__answer"
                          alignment="left"
                          gap="025"
                          width="100"
                          contentsWidth="100"
                        >
                          <Stack gap="025" width="100">
                            <HighlightAnnotationLocation
                              location={{
                                variant: "question",
                                questionId: question.id,
                                itemId: jsonQuestion.id
                              }}
                            >
                              <LabelWithHighlight
                                className="collection-question-block__label"
                                label={jsonQuestion?.text}
                                required={jsonQuestion?.properties?.required}
                                description={jsonQuestion?.description}
                                tooltip={question?.tooltip}
                              />
                            </HighlightAnnotationLocation>
                          </Stack>
                        </Inline>
                        <Box
                          className="collection-question-block__data__cell"
                          width="100"
                        >
                          <Inline
                            className="collection-question-block__answer"
                            alignment="right"
                            gap="050"
                            width="100"
                            contentsWidth="100"
                          >
                            {jsonQuestion.type === QuestionTypes.LIST ? (
                              <QuestionListAnswer
                                d={d}
                                question={
                                  jsonQuestion as Question<ListQuestionProperties>
                                }
                                answer={
                                  answer?.[
                                    jsonQuestion.id
                                  ] as FormAnswer<ListAnswer>
                                }
                                answerAccessor={`${answerAccessor}.${jsonQuestion.id}.value`}
                                location={{
                                  variant: "answer",
                                  questionId: question.id,
                                  itemId: jsonQuestion.id
                                }}
                              />
                            ) : (
                              <QuestionAnswer
                                question={jsonQuestion}
                                answer={
                                  answer?.[jsonQuestion.id]
                                    ?.value as FormAnswer["value"]
                                }
                                answerAccessor={`${answerAccessor}.${jsonQuestion.id}.value`}
                                disabled={
                                  jsonQuestion.properties?.disabled ||
                                  question.properties?.disabled
                                }
                                location={{
                                  variant: "answer",
                                  questionId: question.id,
                                  itemId: jsonQuestion.id
                                }}
                              />
                            )}
                          </Inline>
                        </Box>
                      </Inline>
                    );
                  })}
                </Box>
              )
            )}
          </Accordion>
        </Stack>
      </td>
    </>
  );
};
QuestionJsonAnswer.displayName = "QuestionJsonAnswer";

const newJsonAnswer = (items: Question[]) => {
  const formAnswers: FormAnswer[] = items.map(question => {
    if (question.type === QuestionTypes.JSON) {
      return {
        questionId: question.id,
        value: newJsonAnswer(
          (question.properties as JSONQuestionProperties).items
        ),
        type: question.type
      };
    }

    if (question.type === QuestionTypes.LIST) {
      const itemQuestion = (question.properties as ListQuestionProperties)
        ?.items[0];

      const itemId = customNanoId();
      return {
        questionId: question.id,
        value: {
          entities: {
            [itemId]: {
              id: itemId,
              item: {
                [itemQuestion.id]: {
                  questionId: itemQuestion.id,
                  type: itemQuestion.type
                }
              }
            }
          },
          order: [itemId]
        } as ListAnswer,
        type: question.type
      };
    }

    return {
      questionId: question.id,
      type: question.type
    };
  });

  return formAnswers.reduce<Record<string, (typeof formAnswers)[0]>>(
    (acc, obj) => {
      if (obj.questionId !== undefined) {
        acc[obj.questionId] = obj;
      }
      return acc;
    },
    {}
  );
};
