import React, { useMemo } from "react";

import { Box, Form, MultiSelectValue, Text } from "@oneteam/onetheme";
import { useFormContext } from "react-hook-form";

import { updateAnswer } from "@helpers/forms/answerHelper";

import { OTAIFormField } from "@components/shared/OTAIForm/OTAIFormField";

import { useCollectionFormContext } from "@pages/collection/forms/CollectionFormContext";

import { AnswerLocation } from "@src/types/AnnotationLocation";
import { Question, QuestionTypes } from "@src/types/Question.ts";
import {
  BooleanQuestionProperties,
  DateQuestionRangeValue,
  FilesQuestionProperties,
  TextQuestionProperties
} from "@src/types/QuestionProperties.ts";
import {
  AnswerDocChange,
  BooleanAnswer,
  FormAnswer,
  JsonAnswer,
  ListAnswer,
  TableAnswer
} from "@src/types/collection/CollectionForm.ts";

import { HighlightAnnotationLocation } from "../../HighlightAnnotationLocation/HighlightAnnotationLocation";

export const QuestionAnswer = ({
  question,
  answer,
  answerAccessor,
  disabled,
  location,
  highlightingOff = false
}: {
  question: Question;
  answer: FormAnswer["value"];
  answerAccessor: string;
  disabled?: boolean;
  location?: AnswerLocation;
  highlightingOff?: boolean;
}) => {
  const { documentId, docChange } = useCollectionFormContext();
  // DO not pass the default value to answer for now, as we will use flows to set “default values” later
  // const shouldShowDefault = answer === undefined || answer === "";
  const shouldShowDefault = false;

  const { setValue, trigger } = useFormContext();

  const questionAnswerLocation: AnswerLocation = useMemo(() => {
    if (location) {
      return location;
    }
    return {
      variant: "answer",
      questionId: question.id
    };
  }, [location, question.id]);

  const onChange = (
    question: Question,
    value: FormAnswer["value"],
    answerAccessor: string,
    docChange?: AnswerDocChange
  ) => {
    if (!docChange) {
      if (!answerAccessor) {
        return;
      }
      setValue(answerAccessor, value);
    }
    trigger("answer");
    updateAnswer(question, value, answerAccessor, documentId);
  };

  if (
    // question.type === QuestionTypes.TABLE ||
    question.type === QuestionTypes.JSON
  ) {
    return <></>;
  }

  if (
    [
      QuestionTypes.TEXT,
      QuestionTypes.NUMBER,
      QuestionTypes.TEXT,
      QuestionTypes.DATE,
      QuestionTypes.SELECT,
      QuestionTypes.MULTISELECT,
      QuestionTypes.LIST,
      QuestionTypes.TABLE
    ]
      .map(String)
      .includes(question.type)
  ) {
    return (
      <Box
        width="100"
        alignment={question.type === QuestionTypes.NUMBER ? "right" : "left"}
        contentsWidth={
          disabled && question.type === QuestionTypes.TEXT ? "fit" : "100"
        }
      >
        <HighlightAnnotationLocation
          location={questionAnswerLocation}
          width={
            disabled && question.type === QuestionTypes.TEXT ? "fit" : "100"
          }
          contentsWidth={
            disabled && question.type === QuestionTypes.TEXT ? "fit" : "100"
          }
          hugContainer
          disabled={!location || highlightingOff}
        >
          <OTAIFormField
            id={answerAccessor}
            question={question}
            answer={
              shouldShowDefault
                ? (question as Question<TextQuestionProperties>).properties
                    ?.defaultValue
                : answer
            }
            isLabelExternal
            onChange={value => {
              onChange(
                question,
                value as
                  | string
                  | number
                  | boolean
                  | MultiSelectValue
                  | DateQuestionRangeValue
                  | TableAnswer
                  | JsonAnswer
                  | ListAnswer
                  | undefined,
                answerAccessor,
                docChange
              );
            }}
            disabled={disabled}
            onlyTriggerChangeWhenBlur
          />
        </HighlightAnnotationLocation>
      </Box>
    );
  }

  if (question.type === QuestionTypes.BOOLEAN) {
    const booleanQuestion = question as Question<BooleanQuestionProperties>;
    return (
      <HighlightAnnotationLocation
        width="100"
        contentsWidth="fill"
        hugContainer
        location={questionAnswerLocation}
      >
        <Form.RadioGroup
          width="100"
          name={answerAccessor}
          options={[
            {
              value: true,
              label: booleanQuestion.properties?.trueText ?? "Yes"
            },
            {
              value: false,
              label: booleanQuestion.properties?.falseText ?? "No"
            }
          ]}
          onChange={value => {
            onChange(
              question,
              value as BooleanAnswer,
              answerAccessor,
              docChange
            );
          }}
          value={
            shouldShowDefault
              ? (booleanQuestion.properties?.defaultValue ?? undefined)
              : (answer as boolean | undefined)
          }
          disabled={disabled}
        />
      </HighlightAnnotationLocation>
    );
  }

  if (question.type === QuestionTypes.FILES) {
    const files = question as Question<FilesQuestionProperties>;
    return (
      <HighlightAnnotationLocation
        location={questionAnswerLocation}
        width="100"
        contentsWidth="100"
        hugContainer
        // disabled={highlightingOff}
      >
        <OTAIFormField
          id={answerAccessor}
          isLabelExternal={true}
          question={files}
          answer={answer}
          onChange={data => {
            const change = data as {
              op: string;
              file: { path: string; name: string };
            };

            const value = [];
            if (change.op === "ADD") {
              value.push(change.file);
            } else if (change.op === "DELETE") {
              //set name to undefined to indicate that the file needs to be deleted
              value.push({ path: change.file.path, name: "undefined" });
            }

            updateAnswer(question, value, answerAccessor, documentId);
          }}
          disabled={disabled}
        />
      </HighlightAnnotationLocation>
    );
  }

  return (
    <Text>
      {question.type} is not supported yet. Please contact the support team.
    </Text>
  );
};

QuestionAnswer.displayName = "QuestionAnswer";
