import React, { useCallback, useEffect, useMemo, useState } from "react";

import { AnyDocumentId } from "@automerge/automerge-repo";
import { useDocument, useHandle } from "@automerge/automerge-repo-react-hooks";
import { ChangeFn, ChangeOptions } from "@automerge/automerge/next";
import { Box, Form, Loading } from "@oneteam/onetheme";
import { UseFormReturn } from "react-hook-form";
import { useOutletContext, useParams } from "react-router-dom";
import { z } from "zod";

import { logLinkToDebugPage } from "@helpers/debug.ts";
import { ZodAnswerSchemaBuilder } from "@helpers/forms/ZodAnswerSchemaBuilder/ZodAnswerSchemaBuilder.ts";
import { getQuestionsForAnswers } from "@helpers/forms/collectionHelper.ts";
import { WorkspaceConfigurationHelper } from "@helpers/workspace/WorkspaceConfigurationHelper.ts";

import { CollectionFormPageContent } from "@pages/collection/forms/CollectionFormPageContent/CollectionFormPageContent";
import {
  useFindForm,
  useWorkspaceVersion
} from "@pages/collection/home/<USER>";
import { ErrorElement } from "@pages/errors/ErrorElement";

import { useDictionary } from "@src/hooks/useDictionary.tsx";
import {
  FormAnnotationDocument,
  FormAnswerDocument
} from "@src/types/documentTypes.ts";
import { Workspace } from "@src/types/workspace.ts";

import { useCollectionHomeContext } from "../home/<USER>";
import { useCollectionFormContext } from "./CollectionFormContext";

export const CollectionFormPage = () => {
  const { collectionFormId } = useCollectionHomeContext();

  const { workspaceKey } = useParams();
  const { workspace } = useOutletContext<{ workspace: Workspace }>();
  const { data: workspaceVersion } = useWorkspaceVersion(workspace);

  const d = useDictionary();

  const [form, setForm] = useState<UseFormReturn>();
  const {
    data: formEntity,
    isError,
    isLoading
  } = useFindForm(collectionFormId);

  const [document, docChange] = useDocument<FormAnswerDocument>(
    formEntity?.documentId as AnyDocumentId
  );
  const [annotationDocument, annotationDocChange] =
    useDocument<FormAnnotationDocument>(
      formEntity?.annotationDocumentId as AnyDocumentId
    );

  const formData = useMemo(() => {
    if (!document || !workspaceVersion || !formEntity) {
      return undefined;
    }

    const configuration = workspaceVersion.items[0].configuration;
    const configurationHelper = new WorkspaceConfigurationHelper(configuration);
    const formConfiguration = configurationHelper.getFormOrThrow(
      formEntity.formConfigurationId
    );

    return {
      ...document,
      configuration: formConfiguration,
      foundation: formEntity.foundation,
      interval: formEntity.intervalId
        ? configurationHelper.getIntervalById(formEntity.intervalId)
        : undefined,
      series: formEntity.intervalId
        ? configurationHelper.getSeriesByIntervalId(formEntity.intervalId)
        : undefined
    };
  }, [document, workspaceVersion, formEntity]);

  const schema = useMemo(() => {
    if (!formData) {
      return undefined;
    }

    const questions = getQuestionsForAnswers(
      formData.answers,
      formData.configuration
    );

    return z.object({
      answers: new ZodAnswerSchemaBuilder(
        questions,
        d,
        schema => {
          return z.object({
            value: schema
          });
        },
        false
      ).generatedSchema
    });
  }, [d, formData]);

  useEffect(() => {
    if (form && schema) {
      form.trigger();
    }
  }, [schema, form]);

  const docHandle = useHandle<FormAnswerDocument>(
    formEntity?.documentId as AnyDocumentId
  );

  const docChangeWhenReady = useCallback(
    (
      changeFn: ChangeFn<FormAnswerDocument>,
      options?: ChangeOptions<FormAnswerDocument> | undefined
    ) => {
      docHandle?.whenReady().then(() => {
        docChange(d => {
          try {
            changeFn(d);
          } catch (e) {
            console.error(e);
          }
        }, options);
      });
    },
    [docHandle, docChange]
  );

  const annotationDocHandle = useHandle<FormAnswerDocument>(
    formEntity?.annotationDocumentId as AnyDocumentId
  );

  const annotationDocChangeWhenReady = useCallback(
    (
      changeFn: ChangeFn<FormAnnotationDocument>,
      options?: ChangeOptions<FormAnnotationDocument> | undefined
    ) => {
      annotationDocHandle?.whenReady().then(() => {
        annotationDocChange(d => {
          try {
            changeFn(d);
          } catch (e) {
            console.error(e);
          }
        }, options);
      });
    },
    [annotationDocHandle, annotationDocChange]
  );

  const {
    setFormData,
    setDocChange,
    setDocumentId,
    setFieldState,
    setAnnotationDocChange,
    setAnnotationDocumentId,
    setFormAnnotationDocument
  } = useCollectionFormContext();

  useEffect(() => {
    if (formData) {
      setFormData?.(formData);

      if (formData.answers) {
        Object.entries(formData.answers).forEach(([answerId, answer]) => {
          const answerAccessor = `answers.${answerId}`;
          try {
            schema?.shape.answers.shape[answerId]?.parse(answer);
            setFieldState?.(answerAccessor, { error: undefined });
          } catch (error) {
            if (error instanceof z.ZodError) {
              setFieldState?.(answerAccessor, {
                error: error.errors[0]?.message
              });
            }
          }
        });
      }
    }
  }, [formData, setFormData, setFieldState, schema]);

  useEffect(() => {
    if (formEntity?.documentId) {
      setDocumentId?.(formEntity.documentId);
    }
  }, [formEntity?.documentId, setDocumentId]);

  useEffect(() => {
    if (docChangeWhenReady) {
      setDocChange?.(() => docChangeWhenReady);
    }
  }, [docChangeWhenReady, setDocChange, setAnnotationDocChange]);

  useEffect(() => {
    if (formEntity?.annotationDocumentId) {
      setAnnotationDocumentId?.(formEntity.annotationDocumentId);
    }
  }, [formEntity?.annotationDocumentId, setAnnotationDocumentId]);

  useEffect(() => {
    if (annotationDocChangeWhenReady) {
      setAnnotationDocChange?.(() => annotationDocChangeWhenReady);
    }
  }, [
    docChangeWhenReady,
    setDocChange,
    setAnnotationDocChange,
    annotationDocChangeWhenReady
  ]);

  useEffect(() => {
    if (!annotationDocument) {
      return;
    }
    setFormAnnotationDocument?.(annotationDocument);
  }, [annotationDocument, setFormAnnotationDocument]);

  useEffect(() => {
    if (!formEntity) {
      return;
    }
    // leaving this in so it's visible for those wanting to use the debug console
    logLinkToDebugPage(formEntity.documentId, "answers");
    logLinkToDebugPage(formEntity.annotationDocumentId, "annotations");
  }, [formEntity]);

  if (isError || !collectionFormId) {
    return <ErrorElement />;
  }

  if (
    isLoading ||
    !collectionFormId ||
    !formEntity ||
    !workspaceVersion ||
    !workspaceKey ||
    !document
  ) {
    return (
      <Box height="100" contentsHeight="fill">
        <Loading size={24} />
      </Box>
    );
  }

  return (
    <Form
      schema={schema}
      d={d}
      setForm={setForm}
      hideFormButtons
      defaultValues={formData}
      handleSubmit={() => {}}
    >
      {formData && <CollectionFormPageContent />}
    </Form>
  );
};

CollectionFormPage.displayName = "CollectionFormPage";
