import { ChangeOptions } from "@automerge/automerge";
import { ChangeFn } from "@automerge/automerge-repo";
import { SelectValue } from "@oneteam/onetheme";

import {
  FormAnnotationDocument,
  FormAnswerDocument,
  Resource
} from "@src/types/documentTypes.ts";

import { ConfigurationFormType } from "../FormConfiguration.ts";
import { Foundation } from "../Foundation.ts";
import { Question, QuestionTypeOptions } from "../Question";
import {
  BooleanQuestionValue,
  DateQuestionValue,
  NumberQuestionValue,
  TextQuestionValue
} from "../QuestionProperties";
import { Interval, SeriesConfig } from "../Series.ts";
import { CollectionFoundationType } from "./CollectionFoundation.ts";

export enum CollectionFormMode {
  VIEW = "view",
  EDIT = "edit",
  ANNOTATE = "annotate"
  // RESOLVE = "resolve",
  // COMMENT = "comment",
}

export enum CollectionFormSummaryModalTab {
  ALL = "all",
  ALERTS = "alerts",
  COMMENTS = "comments",
  HIGHLIGHTS = "highlights"
}

// QuestionTypes.TEXT
export type TextAnswer = TextQuestionValue;
// QuestionTypes.NUMBER
export type NumberAnswer = NumberQuestionValue;
// QuestionTypes.SELECT
export type SelectAnswer = SelectValue;
// QuestionTypes.DATE
export type DateAnswer = DateQuestionValue;
// QuestionTypes.BOOLEAN
export type BooleanAnswer = BooleanQuestionValue;
// QuestionTypes.TABLE
export type TableAnswer = Resource<TableAnswerRow>;

export type TableAnswerRow = {
  id: string;
  columns: QuestionAnswers;
};
//QuestionTypes.FILES
export type FilesAnswer = FileAnswer[];
export type FileAnswer = {
  path: string;
  name: string;
};
// QuestionTypes.JSON
export type JsonAnswer = QuestionAnswers;

// QuestionTypes.LIST
export type ListAnswerItem = {
  id: string;
  item: QuestionAnswers;
};
export type ListAnswer = Resource<ListAnswerItem>;

// TODO: change to camel case and use dictionary
export enum AlertType {
  BLOCKER = "blocker",
  WARNING = "warning",
  INFO = "info",
  SUCCESS = "success"
}

export interface FormAnswer<
  P =
    | TextAnswer
    | NumberAnswer
    | SelectAnswer
    | DateAnswer
    | BooleanAnswer
    | FilesAnswer
    | TableAnswer
    | JsonAnswer
    | ListAnswer
> {
  questionId?: Question["id"]; // Not used
  value?: P;
  type: QuestionTypeOptions;
}

export type AnswerDocChange = (
  changeFn: ChangeFn<FormAnswerDocument>,
  options?: ChangeOptions<FormAnswerDocument> | undefined
) => void;

export type FormAnnotationDocChange = (
  changeFn: ChangeFn<FormAnnotationDocument>,
  options?: ChangeOptions<FormAnnotationDocument> | undefined
) => void;

export type QuestionAnswers = {
  [questionId: string]: FormAnswer | { [questionId: string]: FormAnswer };
};

// const FormAnswerHistory = {};
// export interface FormAnswerWithHistory<P>extends FormAnswer<P> & {
//     history: FormAnswerHistory[];
// }
export interface CollectionFormType {
  id: string;
  foundationId: CollectionFoundationType["id"];
  configuration: {
    formId: ConfigurationFormType["id"];
  }; // TODO: move to workspace configuration document storage path
  answers: FormAnswer[];
  status: string;
  series: {
    // Update to match use case - this is for demo
    seriesId: string;
    current: string;
  };
  history: [
    {
      questions: [];
      comment: string;
      createdAt: string;
      createdBy: string; // OneTeam AI or user
    }
  ];
}
export type CollectionFormList = Partial<CollectionFormType>[];

export interface CollectionFormData extends FormAnswerDocument {
  configuration: ConfigurationFormType;
  foundation: Foundation;
  interval: Interval | undefined;
  series: SeriesConfig | undefined;
}
