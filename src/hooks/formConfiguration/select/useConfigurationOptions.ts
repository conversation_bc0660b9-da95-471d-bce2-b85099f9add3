import { WorkspaceConfigurationHelper } from "@helpers/workspace/WorkspaceConfigurationHelper.ts";

import { useDictionary } from "@src/hooks/useDictionary.tsx";
import { DynamicOptionsReturnType } from "@src/types/DynamicSelectOptions.ts";
import { QuestionTypes } from "@src/types/Question";
import {
  DynamicOptionTags,
  SelectQuestionDynamicOptions
} from "@src/types/QuestionProperties.ts";
import { WorkspaceDocument } from "@src/types/documentTypes.ts";

/**
 * This hook gets dynamic options
 * These options are sourced from workspace configuration document (either latest or published)
 *
 *
 * See {@link FormModal} component for usage example with published workspace configuration document
 *
 * See {@link OTAIFormFieldSelectOptionsFromConfiguration} component for usage example with latest workspace configuration document
 */
export const useConfigurationOptions = (
  dynamicOptions: SelectQuestionDynamicOptions | undefined,
  document: WorkspaceDocument
): DynamicOptionsReturnType => {
  const d = useDictionary();

  /**
   * Get foundation configuration options
   * e.g. { value: "1", label: "Client" }
   */
  const getFoundationOptions = () => {
    if (!d) {
      console.error(
        "useConfigurationOptions.getFoundationOptions: dictionary is undefined"
      );
      return { options: [], isFetching: false };
    }
    const foundationConfigs = new WorkspaceConfigurationHelper(
      document
    ).listFoundationsAsOptions();
    return { options: foundationConfigs, isFetching: false };
  };

  /**
   * Form configurations in a specific foundation configuration
   * e.g. value will be formConfigurationId or key and label will be form name
   */
  const getFormOptions = (
    foundationConfigurationId: string | undefined,
    useKeyAsValue = false
  ) => {
    const formConfigs = new WorkspaceConfigurationHelper(
      document
    ).listFormsAsOptions(foundationConfigurationId, useKeyAsValue);
    return { options: formConfigs, isFetching: false };
  };

  /**
   * Questions in a specific form configuration
   * e.g. value will be questionId and label will be question text
   * { value: "1", label: "What is your name" }
   */
  const getFormQuestionOptions = (formConfigurationId: string | undefined) => {
    const formQuestionConfigs = new WorkspaceConfigurationHelper(
      document
    ).listFormQuestionsAsOptions(formConfigurationId, d);
    return { options: formQuestionConfigs, isFetching: false };
  };

  /**
   * Get series configuration options
   * e.g. { value: "1", label: "Year" }
   */
  const getSeriesOptions = () => {
    const seriesConfigs = new WorkspaceConfigurationHelper(
      document
    ).listSeriesAsOptions();
    return { options: seriesConfigs, isFetching: false };
  };

  /**
   * Get interval configuration options
   * e.g. { value: "1", label: "2024" }
   */
  const getFormIntervalOptions = (formConfigurationId?: string) => {
    const intervalConfigs = new WorkspaceConfigurationHelper(
      document
    ).listFormIntervalsAsOptions(formConfigurationId);
    return { options: intervalConfigs, isFetching: false };
  };

  const getSeriesIntervalOptions = (seriesConfigurationId?: string) => {
    const intervalConfigs = new WorkspaceConfigurationHelper(
      document
    ).listSeriesIntervalsAsOptions(seriesConfigurationId);
    return { options: intervalConfigs, isFetching: false };
  };

  const getFlowOptions = () => {
    const flowConfigs = new WorkspaceConfigurationHelper(
      document
    ).listFlowsAsOptions();
    return { options: flowConfigs, isFetching: false };
  };

  /**
   * Get question types
   * e.g. { value: "text", label: "Text" }
   */
  const getQuestionTypes = (skip: string = "") => {
    // skip example: "text, number, select, date, boolean, table, json, files"
    const validQuestionTypeValues = Object.values(QuestionTypes) as string[];
    const skipTypes = (skip ?? "")
      .split(",")
      .map(type => type.trim())
      .filter(type => validQuestionTypeValues.includes(type));
    const questionTypes = validQuestionTypeValues
      .filter(type => !skipTypes?.includes(type))
      .map(type => ({
        value: type,
        label: d(`ui.configuration.forms.question.type.${type}`)
      }));
    return { options: questionTypes, isFetching: false };
  };

  if (!dynamicOptions) {
    return { options: [], isFetching: false };
  }

  const { tag, body } = dynamicOptions;

  switch (tag) {
    case DynamicOptionTags.FOUNDATION_CONFIGURATION_ID:
      return getFoundationOptions();
    case DynamicOptionTags.FORM_CONFIGURATION_ID:
      return getFormOptions(body?.parentFoundationConfigurationId);
    case DynamicOptionTags.FORM_CONFIGURATION_KEY:
      return getFormOptions(body?.parentFoundationConfigurationId, true);
    case DynamicOptionTags.FORM_CONFIGURATION_QUESTION_ID:
      return getFormQuestionOptions(body?.formConfigurationId);
    case DynamicOptionTags.SERIES_CONFIGURATION_ID:
      return getSeriesOptions();
    case DynamicOptionTags.FORM_CONFIGURATION_SERIES_INTERVAL_ID:
      return getFormIntervalOptions(body?.formConfigurationId);
    case DynamicOptionTags.SERIES_INTERVAL_ID:
      return getSeriesIntervalOptions(body?.seriesConfigurationId);
    case DynamicOptionTags.FLOW_CONFIGURATION_ID:
      return getFlowOptions();
    case DynamicOptionTags.QUESTION_TYPES:
      return getQuestionTypes(body?.skip);
    default:
      console.error(
        `useConfigurationProvider.getOptions: tag ${tag} is not supported`
      );
      return { options: [], isFetching: false };
  }
};
